package com.github.cret.web.oee.config;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import com.github.cret.web.oee.utils.UrlEncryptionUtil;

/**
 * 反馈相关配置类，用于存储和管理反馈处理相关的URL配置
 */
@Component
@ConfigurationProperties(prefix = "feedback")
public class FeedbackConfig {

	// 反馈处理基础URL
	private String handleBaseUrl = "http://localhost:3333/feedback/handle";

	// 仪表板基础URL
	private String dashboardBaseUrl = "http://************:3000/dashboard";

	// 默认仪表板路径
	private String defaultDashboardPath = "SMT1-1";

	// URL参数加密配置
	private boolean enableUrlEncryption = true;

	// URL参数加密密钥（Base64编码，256位AES密钥）
	// 注意：在生产环境中应该通过环境变量或配置文件设置此密钥
	// 这是一个有效的256位(32字节)AES密钥的Base64编码
	private String urlEncryptionKey = "YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXoxMjM0NTY=";

	public String getHandleBaseUrl() {
		return handleBaseUrl;
	}

	public void setHandleBaseUrl(String handleBaseUrl) {
		this.handleBaseUrl = handleBaseUrl;
	}

	public String getDashboardBaseUrl() {
		return dashboardBaseUrl;
	}

	public void setDashboardBaseUrl(String dashboardBaseUrl) {
		this.dashboardBaseUrl = dashboardBaseUrl;
	}

	public String getDefaultDashboardPath() {
		return defaultDashboardPath;
	}

	public void setDefaultDashboardPath(String defaultDashboardPath) {
		this.defaultDashboardPath = defaultDashboardPath;
	}

	public boolean isEnableUrlEncryption() {
		return enableUrlEncryption;
	}

	public void setEnableUrlEncryption(boolean enableUrlEncryption) {
		this.enableUrlEncryption = enableUrlEncryption;
	}

	public String getUrlEncryptionKey() {
		return urlEncryptionKey;
	}

	public void setUrlEncryptionKey(String urlEncryptionKey) {
		this.urlEncryptionKey = urlEncryptionKey;
	}

	/**
	 * 构建完整的反馈处理URL（默认为编辑模式）
	 * @param triggerRecordId 触发记录ID
	 * @param sendRecordId 发送记录ID
	 * @return 完整的反馈处理URL
	 */
	public String buildFeedbackHandleUrl(String triggerRecordId, String sendRecordId) {
		return buildFeedbackHandleUrl("edit", triggerRecordId, sendRecordId);
	}

	/**
	 * 构建完整的反馈处理URL（支持不同操作类型）
	 * @param action 操作类型（edit-编辑/填写解决方案, view-查看解决方案）
	 * @param triggerRecordId 触发记录ID
	 * @param sendRecordId 发送记录ID
	 * @return 完整的反馈处理URL
	 */
	public String buildFeedbackHandleUrl(String action, String triggerRecordId, String sendRecordId) {
		return handleBaseUrl + "/" + action + "/" + triggerRecordId + "/" + sendRecordId;
	}

	/**
	 * 构建包含用户信息的反馈处理URL
	 * @param action 操作类型（edit-编辑/填写解决方案, view-查看解决方案）
	 * @param triggerRecordId 触发记录ID
	 * @param sendRecordId 发送记录ID
	 * @param userId 用户ID
	 * @param userName 用户名称
	 * @return 包含用户信息的完整反馈处理URL
	 */
	public String buildFeedbackHandleUrlWithUser(String action, String triggerRecordId, String sendRecordId,
			String userId, String userName) {
		StringBuilder urlBuilder = new StringBuilder();
		urlBuilder.append(handleBaseUrl)
			.append("/")
			.append(action)
			.append("/")
			.append(triggerRecordId)
			.append("/")
			.append(sendRecordId);

		// 如果启用了URL加密，则加密查询参数
		if (enableUrlEncryption && UrlEncryptionUtil.isValidKey(urlEncryptionKey)) {
			try {
				Map<String, String> params = new HashMap<>();
				params.put("userId", userId);
				if (userName != null && !userName.trim().isEmpty()) {
					params.put("userName", userName);
				}

				String encryptedQuery = UrlEncryptionUtil.buildEncryptedQueryString(params, urlEncryptionKey);
				urlBuilder.append(encryptedQuery);
			}
			catch (Exception e) {
				// 如果加密失败，回退到明文参数
				urlBuilder.append("?userId=").append(userId);
				if (userName != null && !userName.trim().isEmpty()) {
					try {
						String encodedUserName = URLEncoder.encode(userName, "UTF-8");
						urlBuilder.append("&userName=").append(encodedUserName);
					}
					catch (Exception encodeException) {
						// 如果编码失败，跳过用户名参数
					}
				}
			}
		}
		else {
			// 未启用加密或密钥无效，使用明文参数
			urlBuilder.append("?userId=").append(userId);
			if (userName != null && !userName.trim().isEmpty()) {
				try {
					String encodedUserName = URLEncoder.encode(userName, "UTF-8");
					urlBuilder.append("&userName=").append(encodedUserName);
				}
				catch (Exception e) {
					// 如果编码失败，跳过用户名参数
				}
			}
		}

		return urlBuilder.toString();
	}

	/**
	 * 构建反馈编辑URL（用于填写解决方案）
	 * @param triggerRecordId 触发记录ID
	 * @param sendRecordId 发送记录ID
	 * @return 编辑URL
	 */
	public String buildFeedbackEditUrl(String triggerRecordId, String sendRecordId) {
		return buildFeedbackHandleUrl("edit", triggerRecordId, sendRecordId);
	}

	/**
	 * 构建包含用户信息的反馈编辑URL（用于填写解决方案）
	 * @param triggerRecordId 触发记录ID
	 * @param sendRecordId 发送记录ID
	 * @param userId 用户ID
	 * @param userName 用户名称
	 * @return 包含用户信息的编辑URL
	 */
	public String buildFeedbackEditUrlWithUser(String triggerRecordId, String sendRecordId, String userId,
			String userName) {
		return buildFeedbackHandleUrlWithUser("edit", triggerRecordId, sendRecordId, userId, userName);
	}

	/**
	 * 构建反馈查看URL（用于查看解决方案）
	 * @param triggerRecordId 触发记录ID
	 * @param sendRecordId 发送记录ID
	 * @return 查看URL
	 */
	public String buildFeedbackViewUrl(String triggerRecordId, String sendRecordId) {
		return buildFeedbackHandleUrl("view", triggerRecordId, sendRecordId);
	}

	/**
	 * 构建包含用户信息的反馈查看URL（用于查看解决方案）
	 * @param triggerRecordId 触发记录ID
	 * @param sendRecordId 发送记录ID
	 * @param userId 用户ID
	 * @param userName 用户名称
	 * @return 包含用户信息的查看URL
	 */
	public String buildFeedbackViewUrlWithUser(String triggerRecordId, String sendRecordId, String userId,
			String userName) {
		return buildFeedbackHandleUrlWithUser("view", triggerRecordId, sendRecordId, userId, userName);
	}

	/**
	 * 构建反馈关闭URL（用于关闭异常）
	 * @param triggerRecordId 触发记录ID
	 * @param sendRecordId 发送记录ID
	 * @return 关闭异常URL
	 */
	public String buildFeedbackCloseUrl(String triggerRecordId, String sendRecordId) {
		return buildFeedbackHandleUrl("close", triggerRecordId, sendRecordId);
	}

	/**
	 * 构建完整的仪表板URL
	 * @param dashboardPath 仪表板路径，如果为null则使用默认路径
	 * @return 完整的仪表板URL
	 */
	public String buildDashboardUrl(String dashboardPath) {
		String path = dashboardPath != null ? dashboardPath : defaultDashboardPath;
		return dashboardBaseUrl + "/" + path;
	}

	/**
	 * 构建默认仪表板URL
	 * @return 默认仪表板URL
	 */
	public String buildDefaultDashboardUrl() {
		return buildDashboardUrl(null);
	}

	/**
	 * 解密URL查询参数（供前端使用）
	 * @param encryptedData 加密的参数数据
	 * @return 解密后的参数Map
	 */
	public Map<String, String> decryptUrlParams(String encryptedData) {
		if (!enableUrlEncryption || !UrlEncryptionUtil.isValidKey(urlEncryptionKey)) {
			return new HashMap<>();
		}

		try {
			return UrlEncryptionUtil.decryptParams(encryptedData, urlEncryptionKey);
		}
		catch (Exception e) {
			// 解密失败，返回空Map
			return new HashMap<>();
		}
	}

	/**
	 * 检查是否启用了URL加密
	 * @return true表示启用了URL加密，false表示未启用
	 */
	public boolean isUrlEncryptionEnabled() {
		return enableUrlEncryption && UrlEncryptionUtil.isValidKey(urlEncryptionKey);
	}

}
